import subprocess


def check_ffmpeg_installed():
    ffmpeg_installed = False
    try:
        # run ffmpeg -version
        result = subprocess.run(
            ["ffmpeg", "-version"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            check=True,
        )
        output = result.stdout + result.stderr
        if "ffmpeg version" in output.lower():
            ffmpeg_installed = True
        return False
    except (subprocess.CalledProcessError, FileNotFoundError):
        ffmpeg_installed = False
    if not ffmpeg_installed:
        error_msg = "FFmpeg is not properly installed on your computer\n"
        error_msg += "\nPlease:\n"
        error_msg += "1. Follow the project installation documentation to properly using uv venv\n"
        raise ValueError(error_msg)
