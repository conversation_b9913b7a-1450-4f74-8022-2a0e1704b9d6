import asyncio
import signal
import sys
from config.config_loader import load_config
from config.logger import setup_logging
from core.utils.util import check_ffmpeg_installed

TAG = __name__
logger = setup_logging()

async def wait_for_exit() -> None:
    """
    Wait for a signal to exit.
    """
    loop = asyncio.get_running_loop()
    stop_event = asyncio.Event()

    if sys.platform != "win32":  # Unix / macOS
        for sig in (signal.SIGINT, signal.SIGTERM):
            loop.add_signal_handler(sig, stop_event.set)
        await stop_event.wait()
    else:
        try:
            await asyncio.Future()
        except KeyboardInterrupt:  # Ctrl‑C
            pass

async def main():
    check_ffmpeg_installed()
    config = load_config()
    logger.bind(tag=TAG).info(f"Server started: {config['server']}")
    logger.info(f"Server started: {config['server']}")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("Server stopped with user shut down")
